
'use client'

import React from 'react';
import dynamic from 'next/dynamic';
import {
  Play, Pause, CheckCircle, XCircle, Send, Edit, ListChecks, BarChartBig, Settings, Users, LogOut, Combine, Truck, Printer, BookCopy, Percent, Mic,
} from 'lucide-react';
import { IconButton } from '@/components/shared/icon-button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; 
import { taskStatusOptions, productTypeOptions, defaultVolumeOptions } from '@/data/mock-data';
// appStore is not directly needed here for plants anymore, as plants are passed via props
import { useUiStore } from '@/store/uiStore';
import type { Plant } from '@/types'; // Import Plant type

const AnnounceVehicleArrivalModal = dynamic(() =>
  import('@/components/modals/announce-vehicle-arrival-modal').then((mod) => mod.AnnounceVehicleArrivalModal)
);

function ActionGroup({ children }: { children: React.ReactNode }) {
  return <div className="flex items-center gap-0.5 p-0.5 bg-background rounded-md border">{children}</div>;
}

interface ActionBarProps {
  plants: Plant[]; // Add plants prop
}

function ActionBar({ plants }: ActionBarProps) { // Destructure plants from props
  const { taskStatusFilter, setTaskStatusFilter } = useUiStore();
  
  return (
    <div className="flex items-center space-x-0.5 flex-wrap gap-0.5">
      <ActionGroup>
        <Select 
          value={taskStatusFilter} 
          onValueChange={setTaskStatusFilter} 
        >
          <SelectTrigger className="w-[110px] h-7 text-xs px-1.5">
            <SelectValue placeholder="任务状态过滤" />
          </SelectTrigger>
          <SelectContent>
            {taskStatusOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Play} tooltipLabel="准备生产" onClick={() => console.log('Prepare Production')} />
        <IconButton icon={Play} tooltipLabel="转到正在进行" onClick={() => console.log('Set to In Progress')} />
        <IconButton icon={Pause} tooltipLabel="暂停任务" onClick={() => console.log('Pause Task')} />
        <IconButton icon={CheckCircle} tooltipLabel="完成任务" onClick={() => console.log('Complete Task')} />
        <IconButton icon={XCircle} tooltipLabel="撤销任务" onClick={() => console.log('Cancel Task')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Send} tooltipLabel="发送生产指令" onClick={() => console.log('Send Production Instruction')} />
        <IconButton icon={Truck} tooltipLabel="安排泵车" onClick={() => console.log('Assign Pump Truck')} />
        <IconButton icon={Combine} tooltipLabel="安排其他车辆" onClick={() => console.log('Assign Other Vehicle')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Edit} tooltipLabel="修改任务" onClick={() => console.log('Edit Task')} />
        <IconButton icon={ListChecks} tooltipLabel="发车明细" onClick={() => console.log('Dispatch Details')} />
        <IconButton icon={BarChartBig} tooltipLabel="生产进度" onClick={() => console.log('Production Progress')} />
      </ActionGroup>
      
      <ActionGroup>
         {/* Use plants from props */}
         {plants && plants.length > 0 && <AnnounceVehicleArrivalModal plants={plants} />} 
        <IconButton icon={Settings} tooltipLabel="系统参数设置" onClick={() => console.log('System Settings')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Users} tooltipLabel="换班" onClick={() => console.log('Shift Change')} />
        <IconButton icon={BookCopy} tooltipLabel="交接班记录" onClick={() => console.log('Shift Log')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Printer} tooltipLabel="罐车出车统计" onClick={() => console.log('Tanker Dispatch Stats')} />
        <IconButton icon={BarChartBig} tooltipLabel="调度工程统计" onClick={() => console.log('Project Dispatch Stats')} />
        <IconButton icon={Truck} tooltipLabel="泵车出车统计" onClick={() => console.log('Pump Truck Dispatch Stats')} />
      </ActionGroup>

      <ActionGroup>
         <Select defaultValue="Normal">
          <SelectTrigger className="w-[100px] h-7 text-xs px-1.5">
            <SelectValue placeholder="缺省方量" />
          </SelectTrigger>
          <SelectContent>
            {defaultVolumeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select defaultValue="All">
          <SelectTrigger className="w-[110px] h-7 text-xs px-1.5">
            <SelectValue placeholder="产品种类过滤" />
          </SelectTrigger>
          <SelectContent>
            {productTypeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>
    </div>
  );
}

const MemoizedActionBar = React.memo(ActionBar);
export { MemoizedActionBar as ActionBar };
