// src/components/sections/task-list/cards/ProductionLinePanel.tsx
'use client';

import React, { useMemo } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { X, Factory, TruckIcon, ArrowRight } from 'lucide-react';
import type { Task, Vehicle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

interface ProductionLinePanelProps {
  isVisible: boolean;
  task: Task;
  productionLineCount: number;
  onClosePanel: () => void;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  activeDragData?: { vehicleNumber?: string; type?: string; vehicle?: Vehicle } | null; // Added vehicle to activeDragData
}

interface ProductionLineSlotProps {
  lineId: string;
  lineNumber: number;
  task: Task;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  activeDragData?: { vehicleNumber?: string; type?: string; vehicle?: Vehicle } | null;
}

const ProductionLineSlot: React.FC<ProductionLineSlotProps> = React.memo(({
  lineId,
  lineNumber,
  task,
  onDropVehicleOnLine,
  activeDragData
}) => {
  const [{ canDrop, isOver }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: (item: DraggableVehicleItem, monitor) => {
      return task.dispatchStatus === 'InProgress';
    },
    drop: (item: DraggableVehicleItem, monitor) => {
      if (monitor.canDrop()) {
        onDropVehicleOnLine(item.vehicle, task.id, lineId);
      }
    },
    collect: (monitor: DropTargetMonitor<DraggableVehicleItem, void>) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const slotStyles = useMemo(() => {
    const baseStyles = cn(
      "border border-dashed rounded w-20 h-8",
      "flex items-center justify-center transition-all duration-200",
      task.dispatchStatus !== 'InProgress' ? "opacity-50 cursor-not-allowed" : "hover:border-primary/60"
    );

    if (isOver && canDrop && activeDragData?.type === ItemTypes.VEHICLE_CARD_DISPATCH) {
      return cn(baseStyles, "border-primary bg-primary/10");
    }
    return cn(baseStyles, "border-muted-foreground/40 bg-transparent");
  }, [isOver, canDrop, task.dispatchStatus, activeDragData]);

  return (
    <div ref={(node) => drop(node)} className={slotStyles}>
      <div className="text-xs font-medium">L{lineNumber}</div>
    </div>
  );
});
ProductionLineSlot.displayName = 'ProductionLineSlot';

export const ProductionLinePanel: React.FC<ProductionLinePanelProps> = React.memo(({
  isVisible,
  task,
  productionLineCount,
  onClosePanel,
  onDropVehicleOnLine,
  activeDragData
}) => {
  const productionLines = useMemo(() => {
    return Array.from({ length: productionLineCount }, (_, index) => ({
      id: `L${index + 1}`,
      number: index + 1,
    }));
  }, [productionLineCount]);

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "absolute top-0 right-0 bg-background/60 backdrop-blur-sm",
        "flex flex-col justify-center items-center gap-2 p-2",
        "transition-transform duration-300",
        isVisible ? "translate-x-0" : "translate-x-full"
      )}
      style={{
        width: '100px',
        maxWidth: '100px',
        height: '100%',
        maxHeight: '100%'
      }}
    >
      {productionLines.slice(0, 2).map((line) => (
        <ProductionLineSlot
          key={line.id}
          lineId={line.id}
          lineNumber={line.number}
          task={task}
          onDropVehicleOnLine={onDropVehicleOnLine}
          activeDragData={activeDragData}
        />
      ))}
    </div>
  );
});
ProductionLinePanel.displayName = 'ProductionLinePanel';
