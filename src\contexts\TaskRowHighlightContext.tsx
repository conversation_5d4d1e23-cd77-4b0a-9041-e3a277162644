// src/contexts/TaskRowHighlightContext.tsx
'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useTaskRowHighlight } from '@/hooks/useTaskRowHighlight';

interface TaskRowHighlightContextType {
  dragOverTaskId: string | null;
  dragOverProductionLineId: string | null;
  setDragOverTaskId: (id: string | null) => void;
  setDragOverProductionLineId: (id: string | null) => void;
  highlightedTaskId: string | null;
  isTaskRowHighlighted: (taskId: string) => boolean;
  getTaskRowClasses: (taskId: string) => string;
}

const TaskRowHighlightContext = createContext<TaskRowHighlightContextType | undefined>(undefined);

interface TaskRowHighlightProviderProps {
  children: React.ReactNode;
}

export const TaskRowHighlightProvider: React.FC<TaskRowHighlightProviderProps> = ({ children }) => {
  const [dragOverTaskId, setDragOverTaskId] = useState<string | null>(null);
  const [dragOverProductionLineId, setDragOverProductionLineId] = useState<string | null>(null);

  // Use the task row highlight hook
  const {
    highlightedTaskId,
    isTaskRowHighlighted,
    getTaskRowClasses,
  } = useTaskRowHighlight({
    dragOverTaskId,
    dragOverProductionLineId,
  });

  // Enhanced setters that also clear other states when needed
  const enhancedSetDragOverTaskId = useCallback((id: string | null) => {
    setDragOverTaskId(id);
    if (id) {
      // Clear production line drag state when task is directly dragged over
      setDragOverProductionLineId(null);
    }
  }, []);

  const enhancedSetDragOverProductionLineId = useCallback((id: string | null) => {
    setDragOverProductionLineId(id);
    // Don't clear task drag state here as production line hover should work alongside task hover
  }, []);

  const contextValue: TaskRowHighlightContextType = {
    dragOverTaskId,
    dragOverProductionLineId,
    setDragOverTaskId: enhancedSetDragOverTaskId,
    setDragOverProductionLineId: enhancedSetDragOverProductionLineId,
    highlightedTaskId,
    isTaskRowHighlighted,
    getTaskRowClasses,
  };

  return (
    <TaskRowHighlightContext.Provider value={contextValue}>
      {children}
    </TaskRowHighlightContext.Provider>
  );
};

export const useTaskRowHighlightContext = (): TaskRowHighlightContextType => {
  const context = useContext(TaskRowHighlightContext);
  if (!context) {
    throw new Error('useTaskRowHighlightContext must be used within a TaskRowHighlightProvider');
  }
  return context;
};

// Hook for components that need to set drag state
export const useDragStateManager = () => {
  const context = useTaskRowHighlightContext();
  
  return {
    setDragOverTaskId: context.setDragOverTaskId,
    setDragOverProductionLineId: context.setDragOverProductionLineId,
    dragOverTaskId: context.dragOverTaskId,
    dragOverProductionLineId: context.dragOverProductionLineId,
  };
};

// Hook for components that need to check highlight state
export const useTaskRowHighlightState = () => {
  const context = useTaskRowHighlightContext();
  
  return {
    highlightedTaskId: context.highlightedTaskId,
    isTaskRowHighlighted: context.isTaskRowHighlighted,
    getTaskRowClasses: context.getTaskRowClasses,
  };
};
