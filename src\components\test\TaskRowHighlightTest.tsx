// src/components/test/TaskRowHighlightTest.tsx
'use client';

import React from 'react';
import { useTaskHighlight } from '@/contexts/TaskRowHighlightContext';

export const TaskRowHighlightTest: React.FC = () => {
  const { highlightedTaskId, setHighlightedTaskId } = useTaskHighlight();

  const testTasks = [
    { id: 'task-1', name: '任务 1' },
    { id: 'task-2', name: '任务 2' },
    { id: 'task-3', name: '任务 3' },
  ];

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold">任务行高亮测试</h3>

      <div className="bg-yellow-100 p-3 rounded border">
        <p className="text-sm">
          <strong>测试说明:</strong> 点击"悬停测试"按钮或悬停在按钮上，观察固定列是否保持正确位置。
          固定列应该始终保持在左侧和右侧，不会因为高亮效果而移位。
        </p>
      </div>
      
      <div className="space-y-2">
        <p>当前高亮任务: {highlightedTaskId || '无'}</p>
        
        <div className="flex gap-2">
          {testTasks.map(task => (
            <button
              key={task.id}
              onClick={() => setHighlightedTaskId(task.id)}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              高亮 {task.name}
            </button>
          ))}
          <button
            onClick={() => setHighlightedTaskId(null)}
            className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清除高亮
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">模拟任务行:</h4>
        {testTasks.map(task => (
          <div
            key={task.id}
            data-task-id={task.id}
            className="p-3 border rounded bg-white"
          >
            <span className="font-medium">{task.name}</span>
            <span className="ml-2 text-sm text-gray-500">ID: {task.id}</span>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">模拟表格行 (带固定列):</h4>
        <div className="overflow-x-auto border rounded" style={{ maxWidth: '600px' }}>
          <table className="border-collapse border min-w-[800px]">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 sticky left-0 bg-white z-10">固定列</th>
              <th className="border p-2">任务名称</th>
              <th className="border p-2">状态</th>
              <th className="border p-2">操作</th>
              <th className="border p-2 sticky right-0 bg-white z-10">固定操作</th>
            </tr>
          </thead>
          <tbody>
            {testTasks.map(task => (
              <tr
                key={task.id}
                data-task-id={task.id}
                className="border"
              >
                <td
                  className="border p-2 sticky left-0 bg-white z-10"
                  style={{ position: 'sticky', left: '0px', backgroundColor: 'white' }}
                >
                  固定{task.id.slice(-1)}
                </td>
                <td className="border p-2">{task.name}</td>
                <td className="border p-2">进行中</td>
                <td className="border p-2">
                  <button
                    onMouseEnter={() => setHighlightedTaskId(task.id)}
                    onMouseLeave={() => setHighlightedTaskId(null)}
                    className="px-2 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                  >
                    悬停测试
                  </button>
                </td>
                <td
                  className="border p-2 sticky right-0 bg-white z-10"
                  style={{ position: 'sticky', right: '0px', backgroundColor: 'white' }}
                >
                  <button className="px-2 py-1 bg-blue-500 text-white rounded text-sm">
                    固定操作
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        </div>
      </div>
    </div>
  );
};
