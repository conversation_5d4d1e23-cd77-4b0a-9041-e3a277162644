// src/components/sections/task-list/cells/ProductionLinesCell.tsx
'use client';

import React, { memo } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import type { Task, DensityStyleValues, Vehicle } from '@/types';
import { cn } from '@/lib/utils';
import { ItemTypes } from '@/constants/dndItemTypes';
import { useDragStateManager } from '@/contexts/TaskRowHighlightContext';

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

interface ProductionLineBoxProps {
  task: Task;
  lineId: string;
  lineNumber: number;
  densityStyles: DensityStyleValues;
  onDropVehicle: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

const ProductionLineBoxComponent: React.FC<ProductionLineBoxProps> = ({
  task,
  lineId,
  lineNumber,
  densityStyles,
  onDropVehicle,
}) => {
  const { setDragOverProductionLineId } = useDragStateManager();

  const [{ canDrop, isOver }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: (item: DraggableVehicleItem, monitor) => {
      return task.dispatchStatus === 'InProgress';
    },
    drop: (item: DraggableVehicleItem, monitor) => {
      if (monitor.canDrop()) {
        onDropVehicle(item.vehicle, task.id, lineId);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
    hover: (item, monitor) => {
      if (monitor.isOver({ shallow: true })) {
        setDragOverProductionLineId(lineId);
      } else {
        setDragOverProductionLineId(null);
      }
    },
  });



  const isActiveDropTarget = isOver && canDrop;

  return (
    <div
      ref={(element) => drop(element)}
      key={`cell-${lineId}-dropzone-${task.id}`}
      data-task-id={task.id}
      data-production-line-id={lineId}
      className={cn(
        'border border-dashed rounded flex items-center justify-center hover:border-accent cursor-pointer transition-all duration-150 flex-shrink-0',
        densityStyles.productionLineBoxSize,
        densityStyles.productionLineBoxFontSize,
        isActiveDropTarget
          ? 'bg-accent/30 border-2 border-dashed border-accent ring-1 ring-accent scale-105 shadow-md z-10'
          : 'border-primary/70 hover:bg-accent/10',
        task.dispatchStatus !== 'InProgress' && 'opacity-50 cursor-not-allowed'
      )}
      title={task.dispatchStatus === 'InProgress' ? `生产线 ${lineId} - 调度至任务 ${task.taskNumber}` : `任务状态非"正在进行"，不可调度`}
    >
      {lineId}
    </div>
  );
};


interface ProductionLinesCellProps {
  task: Task;
  productionLineCount: number;
  densityStyles: DensityStyleValues;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}
const ProductionLineBox = memo(ProductionLineBoxComponent);

const ProductionLinesCellComponent: React.FC<ProductionLinesCellProps> = ({
  task,
  productionLineCount,
  densityStyles,
  onDropVehicleOnLine,
}) => {
  if (task.dispatchStatus !== 'InProgress' || productionLineCount === 0) {
    return <div className="text-center text-xs py-1">-</div>;
  }

  return (
    <div className="flex flex-row items-center justify-around h-full w-full px-0.5 py-0.5"
         style={{ gap: `${densityStyles.productionLineBoxGap || 2}px` }}
    >
      {Array.from({ length: productionLineCount }, (_, i) => i + 1).map(line => {
        const lineId = `L${line}`;
        return (
          <ProductionLineBox
            key={lineId}
            task={task}
            lineId={lineId}
            lineNumber={line}
            densityStyles={densityStyles}
            onDropVehicle={onDropVehicleOnLine}
          />
        );
      })}
    </div>
  );
};
export const ProductionLinesCell = memo(ProductionLinesCellComponent);
