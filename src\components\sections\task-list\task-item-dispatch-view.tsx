// src/components/sections/task-list/task-item-dispatch-view.tsx
'use client';

import React, { useMemo, useCallback } from 'react';
import type { Task, Vehicle, InTaskVehicleCardStyle, VehicleDisplayMode, TaskListDensityMode } from '@/types';
import { cn } from '@/lib/utils';
import { InTaskVehicleCard } from './in-task-vehicle-card';
import { useDragStateManager } from '@/contexts/TaskRowHighlightContext';

interface TaskItemDispatchViewProps {
  task: Task;
  vehicles: Vehicle[];
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  onVehicleDispatchedToLine: (vehicleId: string, productionLineId: string, taskId: string) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  isDragOver: boolean;
  dragOverProductionLineId: string | null;
  handleVehicleDrop: (event: React.DragEvent<HTMLDivElement>, productionLineId: string, taskId: string) => void;
  setDragOverProductionLineId: (id: string | null) => void;
  density: Exclude<TaskListDensityMode, 'card' | ''>;
  vehicleDisplayMode: VehicleDisplayMode;
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string, deliveryOrderId?: string) => void;
  onOpenStyleEditor: () => void;
}

export const TaskItemDispatchView = React.memo(
  function TaskItemDispatchView({
    task,
    vehicles: taskVehicles,
    inTaskVehicleCardStyles,
    productionLineCount,
    onVehicleDispatchedToLine,
    onCancelVehicleDispatch,
    isDragOver, // True if dragging over the parent TaskItemContent
    dragOverProductionLineId, // The specific line ID being hovered over
    handleVehicleDrop,
    setDragOverProductionLineId,
    density,
    vehicleDisplayMode,
    onOpenVehicleCardContextMenu,
    onOpenDeliveryOrderDetailsForVehicle,
    onOpenStyleEditor,
  }: TaskItemDispatchViewProps) {
    // Use context for drag state management
    const { setDragOverProductionLineId: contextSetDragOverProductionLineId } = useDragStateManager();

    // Use context setter instead of prop
    const setDragOverProductionLineIdFinal = contextSetDragOverProductionLineId || setDragOverProductionLineId;

    // 使用useMemo优化样式计算，避免每次渲染重新计算
    const styles = useMemo(() => {
      let lineBoxSizeClasses = 'w-6 h-6';
      let lineBoxFontClasses = 'text-[8px]';
      let dispatchViewPadding = 'py-px';
      let vehicleAreaPadding = 'pr-0.5';
      let lineAreaPadding = 'pl-0.5';
      let lineSpacing = 'space-x-px';
      let vehicleScrollAreaPadding = 'py-px';
      let noVehicleTextSize = 'text-[9px]';
      let noLineTextSize = 'text-[9px]';
  
      if (density === 'compact') {
          lineBoxSizeClasses = 'w-5 h-5';
          lineBoxFontClasses = 'text-[7px]';
          dispatchViewPadding = 'py-0';
          vehicleAreaPadding = 'pr-px';
          lineAreaPadding = 'pl-px';
          lineSpacing = 'gap-px';
          vehicleScrollAreaPadding = 'py-0';
          noVehicleTextSize = 'text-[9px]';
          noLineTextSize = 'text-[9px]';
      } else if (density === 'loose') {
          lineBoxSizeClasses = 'w-7 h-7';
          lineBoxFontClasses = 'text-[9px]';
          dispatchViewPadding = 'py-0.5';
          vehicleAreaPadding = 'pr-1';
          lineAreaPadding = 'pl-1';
          lineSpacing = 'space-x-0.5';
          vehicleScrollAreaPadding = 'py-0.5';
          noVehicleTextSize = 'text-[10px]';
          noLineTextSize = 'text-[9px]';
      }
  
      const lineBoxBaseClasses = cn(
          'border border-dashed rounded flex items-center justify-center text-muted-foreground hover:border-accent cursor-pointer transition-all duration-150 flex-shrink-0',
          lineBoxSizeClasses,
          lineBoxFontClasses
      );

      return {
        lineBoxSizeClasses,
        lineBoxFontClasses,
        dispatchViewPadding,
        vehicleAreaPadding,
        lineAreaPadding,
        lineSpacing, 
        vehicleScrollAreaPadding,
        noVehicleTextSize,
        noLineTextSize,
        lineBoxBaseClasses
      };
    }, [density]);

    // 优化生产线处理函数
    const handleProductionLineInteraction = useCallback((lineNumber: number) => {
      const currentLineId = `L${lineNumber}`;
      
      // 缓存拖拽处理函数
      const onDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (task.dispatchStatus === 'InProgress')
          setDragOverProductionLineIdFinal(currentLineId);
      };
      
      const onDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (task.dispatchStatus === 'InProgress') {
          e.dataTransfer.dropEffect = 'move';
        } else {
          e.dataTransfer.dropEffect = 'none';
        }
      };
      
      const onDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!(e.currentTarget as HTMLElement).contains(e.relatedTarget as Node | null)) {
          setDragOverProductionLineId(null);
        }
      };
      
      const onDrop = (e: React.DragEvent<HTMLDivElement>) => handleVehicleDrop(e, currentLineId, task.id);
      
      // 判断是否是当前拖拽目标
      const isSpecificLineDropTarget = isDragOver && dragOverProductionLineId === currentLineId;
      
      // 计算样式类
      const classNames = cn(
        styles.lineBoxBaseClasses,
        isSpecificLineDropTarget
          ? 'bg-accent/30 border-2 border-dashed border-accent ring-1 ring-accent scale-105 shadow-md'
          : 'border-primary/70 hover:bg-accent/10',
        isDragOver && !isSpecificLineDropTarget && 'opacity-60',
        task.dispatchStatus !== 'InProgress' && 'opacity-50 cursor-not-allowed'
      );
      
      const title = task.dispatchStatus === 'InProgress'
        ? `生产线 ${currentLineId} - 调度至任务 ${task.taskNumber}`
        : `任务状态非"正在进行"，不可调度`;
        
      return {
        currentLineId,
        onDragEnter,
        onDragOver,
        onDragLeave,
        onDrop,
        isSpecificLineDropTarget,
        classNames,
        title
      };
    }, [task, isDragOver, dragOverProductionLineId, setDragOverProductionLineId, handleVehicleDrop, styles.lineBoxBaseClasses]);

    // 优化车辆区域内容渲染
    const vehicleAreaContent = useMemo(() => {
      if (taskVehicles.length > 0) {
        return taskVehicles.map((vehicle) => (
          <InTaskVehicleCard
            key={`dispatch-view-vehicle-${vehicle.id}-${task.id}`}
            vehicle={vehicle}
            task={task}
            vehicleDisplayMode={vehicleDisplayMode}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles}
            productionLineCount={productionLineCount}
            onCancelDispatch={onCancelVehicleDispatch}
            onOpenStyleEditor={onOpenStyleEditor}
            onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetailsForVehicle}
            onOpenContextMenu={(e, vehicle) => onOpenVehicleCardContextMenu(e, vehicle, task)}
            density={density}
          />
        ));
      } else {
        return (
          <p className={cn("text-foreground/70 italic self-center px-1 h-full flex items-center", styles.noVehicleTextSize)}>
            无调度车辆
          </p>
        );
      }
    }, [taskVehicles, task, vehicleDisplayMode, inTaskVehicleCardStyles, productionLineCount, onCancelVehicleDispatch, onOpenStyleEditor, onOpenDeliveryOrderDetailsForVehicle, onOpenVehicleCardContextMenu, density, styles.noVehicleTextSize]);

    // 优化生产线区域内容渲染
    const productionLineAreaContent = useMemo(() => {
      if (productionLineCount > 0) {
        return Array.from({ length: productionLineCount }, (_, i) => i + 1).map(
          (line) => {
            const {
              currentLineId,
              onDragEnter,
              onDragOver,
              onDragLeave,
              onDrop,
              classNames,
              title
            } = handleProductionLineInteraction(line);
            
            return (
              <div
                key={`L${line}-dropzone-${task.id}`}
                data-task-id={task.id}
                data-production-line-id={currentLineId}
                onDragEnter={onDragEnter}
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                onDrop={onDrop}
                className={classNames}
                title={title}
              >
                {currentLineId}
              </div>
            );
          }
        );
      } else {
        return (
          <span className={cn("text-foreground/70 self-center pr-1", styles.noLineTextSize)}>
            无产线
          </span>
        );
      }
    }, [productionLineCount, task.id, handleProductionLineInteraction, styles.noLineTextSize]);

    // 计算容器类名
    const containerClassName = useMemo(() => 
      cn('flex justify-between items-center h-full', styles.dispatchViewPadding), 
      [styles.dispatchViewPadding]
    );

    const vehicleAreaClassName = useMemo(() => 
      cn("flex-1 min-w-0 h-full", styles.vehicleAreaPadding),
      [styles.vehicleAreaPadding]
    );

    const vehicleScrollAreaClassName = useMemo(() =>
      cn(
        'flex flex-row gap-0.5 items-center overflow-x-auto custom-thin-horizontal-scrollbar h-full',
        styles.vehicleScrollAreaPadding
      ),
      [styles.vehicleScrollAreaPadding]
    );

    const lineAreaClassName = useMemo(() =>
      cn(
        "flex-shrink-0 flex flex-row items-center border-l h-full",
        styles.lineAreaPadding,
        density === 'compact' ? 'gap-px' : styles.lineSpacing
      ),
      [styles.lineAreaPadding, styles.lineSpacing, density]
    );

    return (
      <div className={containerClassName}>
        <div className={vehicleAreaClassName}>
          <div className={vehicleScrollAreaClassName}>
            {vehicleAreaContent}
          </div>
        </div>

        <div className={lineAreaClassName}>
          {productionLineAreaContent}
        </div>
      </div>
    );
  },
  // 自定义比较函数提高性能
  (prevProps, nextProps) => {
    // 任务ID相同且关键属性未变时，不需要重新渲染
    return prevProps.task.id === nextProps.task.id &&
           prevProps.isDragOver === nextProps.isDragOver && 
           prevProps.dragOverProductionLineId === nextProps.dragOverProductionLineId &&
           prevProps.density === nextProps.density &&
           prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
           prevProps.vehicles.length === nextProps.vehicles.length &&
           prevProps.productionLineCount === nextProps.productionLineCount;
  }
);
TaskItemDispatchView.displayName = 'TaskItemDispatchView';
