
@import '../styles/sticky-columns.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 添加一个淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* 列宽调整样式增强 */
.cursor-col-resize:hover {
  background-color: hsl(var(--primary) / 50%) !important;
  opacity: 0.7;
}

/* Column resize hover effect */
.column-resize-handle:hover {
  background-color: hsl(var(--primary));
  opacity: 1;
}

/* Column resizing visual feedback */
.column-resizing {
  cursor: col-resize !important;
}

.column-resizing-active {
  background-color: hsl(var(--primary) / 0.1) !important;
  border-left: 2px solid hsl(var(--primary));
  border-right: 2px solid hsl(var(--primary));
  transition: all 0.15s ease;
}

.column-resize-complete {
  background-color: hsl(var(--primary) / 0.2) !important;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

/* 固定列的列宽调整样式 */
th[class*="sticky"] .cursor-col-resize {
  z-index: 50 !important; /* Ensured z-index from type is used, but can keep this as a strong default */
}

/* 自定义细滚动条 */
.custom-thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted) / 70%) transparent;
}

.custom-thin-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted) / 70%);
  border-radius: 6px;
}

.custom-thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted) / 90%);
}

/* 调度车辆列宽度调整手柄在左侧 */
th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles {
  left: 0;
  right: auto;
  width: 8px;
  min-width: 8px;
  max-width: 8px;
  height: 100%;
  z-index: 999; /* High z-index for resizer handle */
  cursor: col-resize;
  position: absolute;
  background: transparent;
  pointer-events: auto;
}

th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles::after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  top: 12%;
  width: 1px;
  height: 76%;
  background: transparent;
  transition: background 0.15s;
  transform: translateX(-50%);
}

th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles:hover::after {
  background: #a3a3a3;
}

th[data-column-id="dispatchedVehicles"] .truncate { /* This seems like a specific class for the inner content of this header */
  padding-left: 14px;
}

/* 消息列角标显示优化 */
td[data-column-id="messages"] {
  overflow: visible !important;
  position: relative;
}

td[data-column-id="messages"] > div {
  overflow: visible !important;
}

/* 确保消息角标不被裁剪 */
.message-badge {
  position: absolute;
  z-index: 30;
  pointer-events: none;
}

/* 调度车辆卡片尺寸强制样式 */
.w-8 {
  width: 32px !important;
}
.w-9 {
  width: 36px !important;
}
.w-10 { 
  width: 40px !important;
}
.w-12 {
  width: 48px !important;
}
.w-14 {
  width: 56px !important;
}
.w-16 {
  width: 64px !important;
}
.w-20 {
  width: 80px !important;
}
.w-24 {
  width: 96px !important;
}

.h-7 {
  height: 28px !important;
}
.h-8 {
  height: 32px !important;
}
.h-9 {
  height: 36px !important;
}
.h-10 {
  height: 40px !important;
}

/* 虚拟化表格滚动优化 */
.virtualized-table-container {
  height: 100%;
  min-height: 0;
  flex: 1 1 0%;
  overflow: auto; /* This is crucial for scrolling */
  position: relative; /* For sticky header positioning */
  contain: layout style paint; /* Optimization */
}

/* 确保表格容器正确处理滚动 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) hsl(var(--background));
  scroll-behavior: smooth;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--accent));
}

/* 修复表格行高度和滚动性能 */
.virtualized-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

/* 分组模式下的容器样式 */
.grouped-mode-container {
  height: auto !important;
  min-height: 100% !important;
  overflow: visible !important;
}

.grouped-mode-container .virtualized-table-container {
  height: auto !important;
  min-height: fit-content !important;
  overflow: visible !important;
}

.virtualized-table tbody tr {
  will-change: transform; /* Performance hint for scrolling */
}

.virtualized-table thead th {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  font-size: 11px !important;
  height: 28px !important;
  min-height: 0 !important;
  max-height: 28px !important;
  line-height: 1.1 !important;
}

/* 表格分割线优化：每个单元格底部分割线，最后一行无分割线 */
.virtualized-table tbody td {
  border-bottom: 1px solid hsl(var(--border));
  background-clip: padding-box;
}

.virtualized-table tbody tr:last-child td {
  border-bottom: none;
}

.virtualized-table tbody tr.virtual-row-performance {
  contain: layout style; /* Allows box-shadow from children (td) to be visible */
}

:root {
    --font-family: var(--font-pingfang-sc, var(--font-geist-sans, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji'));
    --font-mono: var(--font-pingfang-mono, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', monospace);
    
    /* Oceanic Deep Theme - Applied as new default :root */
    --background: 200 100% 97%;
    --foreground: 220 40% 15%;
    --card: 200 100% 95%;
    --card-foreground: 220 40% 15%;
    --popover: 200 100% 97%;
    --popover-foreground: 220 40% 15%;
    --primary: 210 70% 55%;
    --primary-foreground: 200 100% 98%;
    --secondary: 190 50% 85%;
    --secondary-foreground: 220 40% 20%;
    --muted: 190 50% 90%; /* Used for fixed column default background */
    --muted-foreground: 220 40% 45%;
    --accent: 10 80% 65%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 70% 55%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 60% 88%;
    --input: 200 60% 90%;
    --ring: 210 70% 55%;

    /* Status colors from Oceanic Deep */
    --status-success-bg: 150 60% 90%;
    --status-success-fg: 150 70% 25%;
    --status-warning-bg: 50 100% 88%;
    --status-warning-fg: 45 100% 30%;
    /* --status-error-bg: 0 80% 92%; Error color, use destructive */
    /* --status-error-fg: 0 70% 40%; */
    --status-danger-bg: hsl(var(--destructive) / 0.1); /* Using destructive for error */
    --status-danger-fg: hsl(var(--destructive));
    --status-info-bg: 190 80% 90%;
    --status-info-fg: 190 70% 30%;

    /* Chart colors from Oceanic Deep */
    --chart-1: 195 65% 50%;
    --chart-2: 215 75% 60%;
    --chart-3: 180 50% 45%;
    --chart-4: 225 80% 70%;
    --chart-5: 205 60% 55%;

    /* Block title colors from Oceanic Deep */
    --block-title-1: 205 100% 94%;
    --block-title: 205 100% 90%;
    --block-title-deeper: 205 100% 85%;

    /* Default OPAQUE backgrounds for fixed columns from Oceanic Deep */
    --fixed-column-header-background-default: hsl(var(--block-title)); /* Table header background using block-title color */
    --fixed-column-cell-background-default: hsl(var(--background));   /* Fully opaque */


    --radius: 0.5rem; /* Oceanic Deep radius */

    /* Dispatch Reminder Highlight from Oceanic Deep */
    --dispatch-reminder-highlight-bg: 45 100% 80%; 
    --dispatch-reminder-highlight-fg: 45 80% 20%;
}

html.dark {
  --background: 220 30% 12%; 
  --foreground: 220 20% 88%; 
  --card: 220 30% 15%; 
  --card-foreground: 220 20% 88%;
  --popover: 220 25% 10%;
  --popover-foreground: 220 20% 90%;
  --primary: 210 75% 65%; 
  --primary-foreground: 220 20% 10%; 
  --secondary: 190 45% 35%; 
  --secondary-foreground: 220 20% 85%;
  --muted: 190 40% 25%; /* Used for fixed column default background in dark mode */
  --muted-foreground: 220 20% 65%; 
  --accent: 10 70% 60%; 
  --accent-foreground: 0 0% 10%;
  --destructive: 0 60% 50%; 
  --destructive-foreground: 0 0% 98%;
  --border: 200 40% 28%; 
  --input: 200 40% 25%; 
  --ring: 210 75% 65%; 

  /* Status colors from Oceanic Deep - Dark Theme */
  --status-success-bg: 150 70% 20%;
  --status-success-fg: 150 60% 85%;
  --status-warning-bg: 45 100% 25%;
  --status-warning-fg: 45 80% 80%;
  /* --status-error-bg: 0 70% 25%; */
  /* --status-error-fg: 0 80% 85%; */
  --status-danger-bg: hsl(var(--destructive) / 0.15);
  --status-danger-fg: hsl(var(--destructive));
  --status-info-bg: 190 70% 20%;
  --status-info-fg: 190 80% 80%;

  /* Chart colors from Oceanic Deep - Dark Theme */
  --chart-1: 195 70% 60%;
  --chart-2: 215 80% 70%;
  --chart-3: 180 55% 55%;
  --chart-4: 225 85% 75%;
  --chart-5: 205 65% 65%;

  /* Block title colors from Oceanic Deep - Dark Theme */
  --block-title-1: 205 35% 20%;
  --block-title: 205 35% 18%;
  --block-title-deeper: 205 35% 15%;

  /* Default OPAQUE backgrounds for fixed columns from Oceanic Deep - Dark Theme */
  --fixed-column-header-background-default: hsl(var(--block-title)); /* Table header background using block-title color */
  --fixed-column-cell-background-default: hsl(var(--background));    /* Fully opaque */
  
  /* Dispatch Reminder Highlight from Oceanic Deep - Dark Theme */
  --dispatch-reminder-highlight-bg: 45 100% 22%; 
  --dispatch-reminder-highlight-fg: 45 80% 75%;
}

/* Horizontal scrollbar for specific elements if needed beyond custom-thin-scrollbar */
.custom-thin-horizontal-scrollbar::-webkit-scrollbar {
  height: 4px; /* Or match custom-thin-scrollbar */
}
.custom-thin-horizontal-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.custom-thin-horizontal-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted) / 70%);
  border-radius: 6px;
}
.custom-thin-horizontal-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted) / 90%);
}
.custom-thin-horizontal-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted) / 70%) transparent;
}

.task-row-dispatch-due {
  background-color: hsl(var(--dispatch-reminder-highlight-bg) / 0.6) !important; /* Ensure this overrides other backgrounds */
  color: hsl(var(--dispatch-reminder-highlight-fg)) !important;
  /* Add a subtle pulse animation */
  animation: pulse-dispatch-due 2s infinite ease-in-out;
}

@keyframes pulse-dispatch-due {
  0%, 100% {
    background-color: hsl(var(--dispatch-reminder-highlight-bg) / 0.6);
  }
  50% {
    background-color: hsl(var(--dispatch-reminder-highlight-bg) / 0.8);
  }
}

/* 车辆卡片拖拽动效优化 */
.vehicle-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* 拖拽开始时的样式 */
.vehicle-card-dragging {
  transform: scale(1.08) rotate(2deg);
  opacity: 0.85;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(59, 130, 246, 0.3);
  z-index: 1000;
  cursor: grabbing;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 拖拽悬停目标样式 */
.vehicle-card-drag-over {
  border: 2px dashed hsl(var(--primary) / 0.6);
  background-color: hsl(var(--primary) / 0.05);
  transform: scale(1.02);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 让位动画效果 */
.vehicle-card-spacing {
  transform: translateX(8px);
  margin-left: 4px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 拖拽预览样式增强 */
.vehicle-drag-preview {
  transform: scale(1.1) rotate(3deg);
  opacity: 0.9;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid hsl(var(--primary) / 0.3);
  backdrop-filter: blur(4px);
  transition: none;
}

/* 悬停时的微妙动效 */
.vehicle-card:hover:not(.vehicle-card-dragging) {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 拖拽区域高亮 */
.drop-zone-active {
  background-color: hsl(var(--primary) / 0.03);
  border: 1px dashed hsl(var(--primary) / 0.3);
  transition: all 0.2s ease-in-out;
}

/* 拖拽时其他卡片的淡化效果 */
.vehicle-card-container.dragging-active .vehicle-card:not(.vehicle-card-dragging) {
  opacity: 0.6;
  filter: blur(0.5px);
  transition: all 0.2s ease-in-out;
}

/* 拖拽完成时的回弹动画 */
@keyframes drop-bounce {
  0% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.vehicle-card-drop-complete {
  animation: drop-bounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 任务行高亮效果 - 简洁设计，完全不影响固定列定位 */
.task-row-highlight {
  background-color: hsl(var(--primary) / 0.08) !important;
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 表格行高亮 - 只使用背景色，完全不影响布局和定位 */
tr.task-row-highlight {
  /* 不添加任何边框、阴影或定位相关的样式 */
}

/* 普通单元格继承高亮背景 */
tr.task-row-highlight td:not([style*="position: sticky"]):not(.sticky-column) {
  background-color: inherit !important;
}

/* 固定列单元格 - 完全保持原有样式，只在内部添加轻微指示 */
tr.task-row-highlight td[style*="position: sticky"],
tr.task-row-highlight td.sticky-column {
  /* 保持所有原有样式，不覆盖任何属性 */
  position: sticky !important; /* 确保sticky定位不被覆盖 */
}

/* 为固定列添加内部高亮指示，不影响布局 */
tr.task-row-highlight td[style*="position: sticky"]::before,
tr.task-row-highlight td.sticky-column::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: hsl(var(--primary) / 0.7);
  z-index: 1;
  pointer-events: none;
}

/* 卡片视图高亮 - 保持原有样式 */
.task-card.task-row-highlight {
  border-left: 4px solid hsl(var(--primary) / 0.8) !important;
  transform: translateX(2px) !important;
}

/* 轻微的脉动效果 - 只影响背景色，不影响布局 */
@keyframes task-row-glow {
  0%, 100% {
    background-color: hsl(var(--primary) / 0.08);
  }
  50% {
    background-color: hsl(var(--primary) / 0.12);
  }
}

.task-row-highlight {
  animation: task-row-glow 2s ease-in-out infinite;
}

/* 固定列指示器的脉动效果 - 只影响伪元素 */
@keyframes sticky-indicator-glow {
  0%, 100% {
    background-color: hsl(var(--primary) / 0.7);
  }
  50% {
    background-color: hsl(var(--primary) / 0.9);
  }
}

tr.task-row-highlight td[style*="position: sticky"]::before,
tr.task-row-highlight td.sticky-column::before {
  animation: sticky-indicator-glow 2s ease-in-out infinite;
}

/* 任务行/卡片选中状态样式 */
.task-row-selected {
  background-color: hsl(var(--primary) / 0.12) !important;
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 表格行选中状态 - 绝对不影响布局和定位 */
tr.task-row-selected {
  /* 只使用背景色，完全不影响布局 */
}

/* 固定列单元格 - 确保保持原有定位 */
tr.task-row-selected td[style*="position: sticky"],
tr.task-row-selected td.sticky-column {
  position: sticky !important; /* 强制保持sticky定位 */
  /* 不覆盖任何其他样式，保持原有定位值 */
}

/* 选中状态的左边框指示 - 使用第一个单元格的边框 */
tr.task-row-selected td:first-child {
  border-left: 4px solid hsl(var(--primary)) !important;
  /* 如果第一个单元格是固定列，确保不影响其定位 */
}

/* 如果第一个单元格是固定列，使用box-shadow代替border */
tr.task-row-selected td:first-child[style*="position: sticky"] {
  border-left: none !important;
  box-shadow: inset 4px 0 0 hsl(var(--primary)) !important;
}

/* 卡片选中状态 */
.task-card.task-row-selected {
  border-left: 4px solid hsl(var(--primary)) !important;
  transform: translateX(3px) !important;
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.25) !important;
  border: 1px solid hsl(var(--primary) / 0.3) !important;
  border-left: 4px solid hsl(var(--primary)) !important; /* 重申左边框 */
}

/* 选中状态优先级高于高亮状态 */
.task-row-selected.task-row-highlight {
  background-color: hsl(var(--primary) / 0.15) !important;
}

/* 选中状态切换动画 */
@keyframes task-select-in {
  0% {
    background-color: transparent;
    transform: translateX(0);
  }
  50% {
    background-color: hsl(var(--primary) / 0.08);
  }
  100% {
    background-color: hsl(var(--primary) / 0.12);
    transform: translateX(3px);
  }
}

@keyframes task-select-out {
  0% {
    background-color: hsl(var(--primary) / 0.12);
    transform: translateX(3px);
  }
  100% {
    background-color: transparent;
    transform: translateX(0);
  }
}

/* 为卡片添加选中动画 */
.task-card.task-row-selected {
  animation: task-select-in 0.3s ease-out forwards;
}

/* 行选中状态的脉动效果 */
@keyframes selected-row-pulse {
  0%, 100% {
    background-color: hsl(var(--primary) / 0.12);
  }
  50% {
    background-color: hsl(var(--primary) / 0.16);
  }
}

.task-row-selected {
  animation: selected-row-pulse 3s ease-in-out infinite;
}

/* 行点击区域样式 */
.task-row-clickable {
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.task-row-clickable:hover:not(.task-row-selected) {
  background-color: hsl(var(--muted) / 0.5);
}

/* 卡片点击区域样式 */
.task-card-clickable {
  cursor: pointer;
  transition: all 0.15s ease;
}

.task-card-clickable:hover:not(.task-row-selected) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px hsl(var(--border) / 0.3);
}

/* Column reordering drag and drop animations */
.column-reordering {
  cursor: grabbing !important;
}

.column-dragging {
  opacity: 0.5 !important;
  transform: scale(0.98);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.column-drag-over {
  background-color: hsl(var(--primary) / 0.1) !important;
  border-left: 3px solid hsl(var(--primary));
  border-right: 3px solid hsl(var(--primary));
  transform: scale(1.02);
  transition: all 0.2s ease;
}

@keyframes columnDropComplete {
  0% { 
    transform: scale(1);
    background-color: hsl(var(--primary) / 0.2);
  }
  50% { 
    transform: scale(1.05);
    background-color: hsl(var(--primary) / 0.3);
  }
  100% { 
    transform: scale(1);
    background-color: transparent;
  }
}

.column-drop-complete {
  animation: columnDropComplete 0.3s ease;
}
