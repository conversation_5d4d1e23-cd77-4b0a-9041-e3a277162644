// src/components/test/TaskCardSingleSelectionTest.tsx
'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTaskSelectionState, useTaskSelectionActions, TaskSelectionProvider } from '@/contexts/TaskSelectionContext';
import { cn } from '@/lib/utils';
import { CheckCircle2, Circle } from 'lucide-react';

const TaskCardSingleSelectionTestContent: React.FC = () => {
  const { selectedTask, selectedTaskId, isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask, clearSelection } = useTaskSelectionActions();

  const testTasks = [
    { 
      id: 'task-1', 
      taskNumber: 'T001', 
      projectName: '万科城市花园',
      dispatchStatus: 'InProgress' as const,
      strength: 'C30',
      volume: 120,
      isDueForDispatch: false
    },
    { 
      id: 'task-2', 
      taskNumber: 'T002', 
      projectName: '恒大绿洲',
      dispatchStatus: 'Pending' as const,
      strength: 'C25',
      volume: 80,
      isDueForDispatch: true
    },
    { 
      id: 'task-3', 
      taskNumber: 'T003', 
      projectName: '碧桂园凤凰城',
      dispatchStatus: 'InProgress' as const,
      strength: 'C35',
      volume: 150,
      isDueForDispatch: false
    },
    { 
      id: 'task-4', 
      taskNumber: 'T004', 
      projectName: '保利香槟国际',
      dispatchStatus: 'Completed' as const,
      strength: 'C30',
      volume: 100,
      isDueForDispatch: false
    },
  ];

  const handleCardClick = (task: any, e: React.MouseEvent) => {
    // 避免在点击按钮时触发卡片选中
    const target = e.target as HTMLElement;
    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      return;
    }
    
    // 切换选中状态
    const isCurrentlySelected = isTaskSelected(task.id);
    if (isCurrentlySelected) {
      setSelectedTask(null);
    } else {
      setSelectedTask(task);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'InProgress': return 'bg-green-100 text-green-700';
      case 'Pending': return 'bg-yellow-100 text-yellow-700';
      case 'Completed': return 'bg-blue-100 text-blue-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'InProgress': return '进行中';
      case 'Pending': return '待开始';
      case 'Completed': return '已完成';
      default: return status;
    }
  };

  // 检查是否有选中的任务
  const hasSelectedTask = !!selectedTaskId;

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">任务卡片单选功能测试</h3>
        <div className="bg-blue-50 p-4 rounded-lg border">
          <p className="text-sm text-blue-800">
            <strong>功能说明:</strong><br/>
            • 点击任务卡片可以选中任务，再次点击取消选中<br/>
            • 同时只能选中一个任务（单选模式）<br/>
            • 选中的卡片会显示明显的accent颜色背景、边框和阴影效果<br/>
            • 选中状态有轻微的位移和缩放动画，以及持续的脉动效果<br/>
            • 点击卡片内的按钮不会触发选中状态变化<br/>
            • 配置支持localStorage持久化存储<br/>
            • <strong>修复:</strong> 已修复TaskSelectionContext中setSelectedTask函数映射错误的问题
          </p>
        </div>
      </div>

      {/* 当前选中状态 */}
      <div className="space-y-3">
        <h4 className="font-medium">当前选中状态:</h4>
        {hasSelectedTask ? (
          <div className="flex items-center gap-3 p-3 bg-accent/10 border border-accent/20 rounded-lg">
            <CheckCircle2 className="w-5 h-5 text-accent" />
            <div>
              <div className="font-medium">已选中: {selectedTask?.taskNumber}</div>
              <div className="text-sm text-muted-foreground">{selectedTask?.projectName}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-3 p-3 bg-muted/50 border border-muted rounded-lg">
            <Circle className="w-5 h-5 text-muted-foreground" />
            <div className="text-muted-foreground">未选中任务</div>
          </div>
        )}
      </div>

      {/* 操作栏按钮状态模拟 */}
      <div className="space-y-3">
        <h4 className="font-medium">操作栏按钮状态:</h4>
        <div className="flex gap-2 flex-wrap">
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            准备生产
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            转到正在进行
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask || selectedTask?.dispatchStatus !== 'InProgress'}
            variant={hasSelectedTask && selectedTask?.dispatchStatus === 'InProgress' ? "default" : "secondary"}
          >
            暂停任务
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "destructive" : "secondary"}
          >
            撤销任务
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">
          {hasSelectedTask 
            ? `按钮已启用，将操作任务: ${selectedTask?.taskNumber}` 
            : '所有按钮已禁用，请先选择一个任务'
          }
        </p>
      </div>

      {/* 任务卡片网格 */}
      <div className="space-y-3">
        <h4 className="font-medium">任务卡片 (点击选中/取消选中):</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {testTasks.map(task => {
            const isSelected = isTaskSelected(task.id);
            
            return (
              <Card
                key={task.id}
                data-task-id={task.id}
                onClick={(e) => handleCardClick(task, e)}
                className={cn(
                  "p-4 cursor-pointer transition-all duration-200 ease-in-out task-card task-card-clickable",
                  "hover:shadow-md",
                  isSelected && "task-row-selected",
                  task.isDueForDispatch && "ring-2 ring-orange-200"
                )}
              >
                <div className="space-y-3">
                  {/* 卡片头部 */}
                  <div className="flex justify-between items-start">
                    <div className="font-semibold text-sm">{task.taskNumber}</div>
                    <div className="flex items-center gap-2">
                      {isSelected && <CheckCircle2 className="w-4 h-4 text-accent" />}
                      <Badge className={cn("text-xs", getStatusColor(task.dispatchStatus))}>
                        {getStatusText(task.dispatchStatus)}
                      </Badge>
                    </div>
                  </div>
                  
                  {/* 项目信息 */}
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-gray-900">{task.projectName}</div>
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>强度: {task.strength}</span>
                      <span>方量: {task.volume}m³</span>
                    </div>
                  </div>
                  
                  {/* 提醒标识 */}
                  {task.isDueForDispatch && (
                    <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                      ⚠️ 待发车提醒
                    </div>
                  )}
                  
                  {/* 测试按钮 - 点击这个不应该触发选中 */}
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      alert(`点击了 ${task.taskNumber} 的详情按钮`);
                    }}
                  >
                    查看详情
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 手动控制 */}
      <div className="space-y-3">
        <h4 className="font-medium">手动控制:</h4>
        <div className="flex gap-2 flex-wrap">
          {testTasks.map(task => (
            <Button
              key={task.id}
              size="sm"
              variant={isTaskSelected(task.id) ? "default" : "outline"}
              onClick={() => {
                if (isTaskSelected(task.id)) {
                  clearSelection();
                } else {
                  setSelectedTask(task as any);
                }
              }}
            >
              {isTaskSelected(task.id) ? '取消选中' : '选中'} {task.taskNumber}
            </Button>
          ))}
          <Button
            size="sm"
            variant="secondary"
            onClick={clearSelection}
            disabled={!hasSelectedTask}
          >
            清除所有选中
          </Button>
        </div>
      </div>
    </div>
  );
};

// 包装器组件，提供必要的Context
export const TaskCardSingleSelectionTest: React.FC = () => {
  return (
    <TaskSelectionProvider>
      <TaskCardSingleSelectionTestContent />
    </TaskSelectionProvider>
  );
};
