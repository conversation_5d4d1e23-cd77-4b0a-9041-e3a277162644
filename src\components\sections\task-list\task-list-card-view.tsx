// src/components/sections/task-list/task-list-card-view.tsx
'use client';

import React, { useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { Task, Vehicle, TaskListStoredSettings, TaskListDensityMode, VehicleDisplayMode } from '@/types';
import { TaskItemContent } from './task-item-content';
import { useAppStore } from '@/store/appStore';
import { useTaskSelectionActions, useTaskSelectionState } from '@/contexts/TaskSelectionContext';

interface TaskListCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  productionLineCount: number;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  dragOverTaskId: string | null;
  setDragOverTaskId: (id: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (id: string | null) => void;
  handleVehicleDrop: (event: React.DragEvent<HTMLDivElement>, productionLineId: string | null, taskId: string) => void;
  handleTaskContextMenu: (event: React.MouseEvent, taskId: string) => void;
  handleRowDoubleClick: (task: Task) => void;
  getStatusLabelProps: (status?: Task['dispatchStatus']) => { label: string; className: string };
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string, deliveryOrderId?: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicleId: string, productionLineId: string, taskId: string) => void;
}

export const TaskListCardView = React.memo(function TaskListCardView({
  filteredTasks,
  vehicles,
  settings,
  productionLineCount,
  vehicleDisplayMode,
  taskStatusFilter,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  handleVehicleDrop,
  handleTaskContextMenu,
  handleRowDoubleClick,
  getStatusLabelProps,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
}: TaskListCardViewProps) {
  const {
    density,
    inTaskVehicleCardStyles,
  } = settings;
  const selectedPlantId = useAppStore(state => state.selectedPlantId);
  const { setSelectedTask } = useTaskSelectionActions();
  const { isTaskSelected } = useTaskSelectionState();

  const handleCardClick = useCallback((task: Task, e: React.MouseEvent) => {
    console.log('Card clicked:', task.taskNumber, task.id);

    // 避免在点击按钮或其他交互元素时触发卡片选中
    const target = e.target as HTMLElement;
    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      console.log('Click ignored - target is interactive element');
      return;
    }

    // 切换选中状态：如果已选中则取消选中，否则选中
    const isCurrentlySelected = isTaskSelected(task.id);
    console.log('Current selection state:', isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log('Deselecting task:', task.taskNumber);
      setSelectedTask(null);
    } else {
      console.log('Selecting task:', task.taskNumber);
      setSelectedTask(task);
    }
  }, [setSelectedTask, isTaskSelected]);


  if (filteredTasks.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground p-4">
        {selectedPlantId ? '此搅拌站当前状态无任务。' : '请先选择一个搅拌站。'}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-0.5 p-0.5">
      {filteredTasks.map((task) => {
        const taskSpecificVehicles = vehicles.filter(v => v.assignedTaskId === task.id);
        const isCurrentDragTarget = dragOverTaskId === task.id && task.dispatchStatus === 'InProgress';
        const isSelected = isTaskSelected(task.id);
        console.log(`Card ${task.taskNumber} (${task.id}) - isSelected:`, isSelected);

        return (
          <Card
            key={task.id}
            data-task-id={task.id}
            data-row-id={task.id}
            onClick={(e) => handleCardClick(task, e)}
            className={cn(
              "flex flex-col transition-all duration-150 ease-in-out rounded-sm shadow-sm overflow-hidden task-card",
              "task-card-clickable", // 添加点击样式
              settings.density === 'compact' ? "h-[38px]" : settings.density === 'loose' ? "h-[48px]" : "h-[44px]",
              isCurrentDragTarget
                ? 'bg-primary/10 z-10'
                : (dragOverTaskId && dragOverTaskId !== task.id ? 'opacity-60' : ''),
              task.isDueForDispatch && "task-row-dispatch-due",
              isSelected && "task-row-selected" // 添加选中样式
            )}
            onDragEnter={(e) => {
              if (task.dispatchStatus === 'InProgress' && e.dataTransfer.types.includes('text/plain')) {
                setDragOverTaskId(task.id);
              }
            }}
            onDragOver={(e) => {
              if (task.dispatchStatus === 'InProgress' && e.dataTransfer.types.includes('text/plain')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
              } else {
                e.dataTransfer.dropEffect = 'none';
              }
            }}
            onDragLeave={(e) => {
              if (!(e.currentTarget as HTMLElement).contains(e.relatedTarget as Node | null)) {
                   if (dragOverTaskId === task.id) setDragOverTaskId(null);
              }
            }}
            onDrop={(e) => handleVehicleDrop(e, task.id, '')}
            onContextMenu={(e) => handleTaskContextMenu(e, task.id)}
            onDoubleClick={() => handleRowDoubleClick(task)}
          >
            <TaskItemContent
              task={task}
              taskVehicles={taskSpecificVehicles}
              inTaskVehicleCardStyles={inTaskVehicleCardStyles}
              productionLineCount={productionLineCount}
              onVehicleDispatchedToLine={onVehicleDispatchedToLine}
              onCancelVehicleDispatch={onCancelVehicleDispatch}
              density={density}
              isDragOver={isCurrentDragTarget} 
              handleVehicleDrop={handleVehicleDrop}
              setDragOverProductionLineId={setDragOverProductionLineId}
              dragOverProductionLineId={dragOverProductionLineId}
              vehicleDisplayMode={vehicleDisplayMode}
              taskStatusFilter={taskStatusFilter}
              getStatusLabelProps={getStatusLabelProps}
              onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
              onOpenDeliveryOrderDetailsForVehicle={onOpenDeliveryOrderDetailsForVehicle}
              onOpenStyleEditor={onOpenStyleEditor}
              isParentDragTarget={isCurrentDragTarget} 
            />
          </Card>
        );
      })}
    </div>
  );
});
TaskListCardView.displayName = 'TaskListCardView';
