// src/hooks/useTaskRowHighlight.ts
import { useState, useCallback, useEffect } from 'react';

interface UseTaskRowHighlightProps {
  dragOverProductionLineId: string | null;
  dragOverTaskId: string | null;
}

interface TaskRowHighlightState {
  highlightedTaskId: string | null;
  highlightedProductionLineId: string | null;
}

/**
 * Hook to manage task row highlighting during vehicle drag operations
 * Highlights the task row when hovering over production lines during drag
 */
export function useTaskRowHighlight({
  dragOverProductionLineId,
  dragOverTaskId,
}: UseTaskRowHighlightProps) {
  const [highlightState, setHighlightState] = useState<TaskRowHighlightState>({
    highlightedTaskId: null,
    highlightedProductionLineId: null,
  });

  /**
   * Extract task ID from production line context
   * Production lines are associated with tasks through DOM structure and data attributes
   */
  const getTaskIdFromProductionLine = useCallback((productionLineId: string | null): string | null => {
    if (!productionLineId) return null;

    console.log('🔍 Looking for task ID from production line:', productionLineId);

    // Strategy 1: Find production line elements with data-task-id attribute
    const productionLineElements = document.querySelectorAll(`[data-production-line-id="${productionLineId}"]`);
    console.log(`🔍 Found ${productionLineElements.length} production line elements`);

    for (const element of productionLineElements) {
      const taskId = element.getAttribute('data-task-id');
      console.log('📍 Production line element task ID:', taskId);
      if (taskId) {
        console.log('✅ Found task ID from production line element:', taskId);
        return taskId;
      }
    }

    // Strategy 2: Find by title attribute (fallback for older elements)
    const titleElements = document.querySelectorAll(`[title*="${productionLineId}"]`);

    for (const element of titleElements) {
      // Check direct data attributes first
      const taskId = element.getAttribute('data-task-id');
      if (taskId) {
        return taskId;
      }

      // Look for task container in parent hierarchy
      let current = element.parentElement;
      while (current) {
        // Check for task ID in data attributes
        const parentTaskId = current.getAttribute('data-task-id') ||
                            current.getAttribute('data-row-id');

        if (parentTaskId && parentTaskId.match(/^[a-f0-9-]{36}$/)) {
          return parentTaskId;
        }

        // Check for task row class patterns
        if (current.classList.contains('tr') ||
            current.classList.contains('task-card') ||
            current.classList.contains('task-row')) {
          // Try to extract task ID from key or other attributes
          const rowKey = current.getAttribute('key');
          if (rowKey && rowKey.match(/^[a-f0-9-]{36}$/)) {
            return rowKey;
          }
        }

        current = current.parentElement;
      }
    }

    return null;
  }, []);

  /**
   * Apply highlighting CSS class to task row
   */
  const applyTaskRowHighlight = useCallback((taskId: string | null) => {
    // Remove existing highlights
    document.querySelectorAll('.task-row-drag-highlight').forEach(el => {
      el.classList.remove('task-row-drag-highlight');
    });

    if (!taskId) return;

    // Find and highlight the corresponding task row
    const selectors = [
      `[data-task-id="${taskId}"]`,
      `[data-row-id="${taskId}"]`,
      `[key="${taskId}"]`,
      `#task-${taskId}`,
      `tr[data-index]:has([data-task-id="${taskId}"])`,
      `.task-card[key="${taskId}"]`,
    ];

    for (const selector of selectors) {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          element.classList.add('task-row-drag-highlight');
        });
        if (elements.length > 0) break;
      } catch (error) {
        // Skip invalid selectors
        continue;
      }
    }
  }, []);

  /**
   * Enhanced task row highlighting that works with both table and card views
   */
  const enhancedApplyHighlight = useCallback((taskId: string | null) => {
    // Remove existing highlights
    const existingHighlights = document.querySelectorAll('.task-row-drag-highlight');
    console.log('🧹 Removing existing highlights:', existingHighlights.length);
    existingHighlights.forEach(el => {
      el.classList.remove('task-row-drag-highlight');
    });

    if (!taskId) {
      console.log('❌ No task ID provided, clearing highlights');
      return;
    }

    console.log('🔍 Looking for task elements with ID:', taskId);

    // Strategy 1: Find by task ID in various attributes
    let found = false;
    const taskSelectors = [
      `[data-task-id="${taskId}"]`,
      `[data-row-id="${taskId}"]`,
      `[key="${taskId}"]`,
      `#task-${taskId}`,
    ];

    for (const selector of taskSelectors) {
      const elements = document.querySelectorAll(selector);
      console.log(`🔍 Selector "${selector}" found ${elements.length} elements`);
      if (elements.length > 0) {
        elements.forEach(el => {
          console.log('📍 Found element:', el.tagName, el.className);
          // For table rows, highlight the entire row
          if (el.tagName === 'TR') {
            el.classList.add('task-row-drag-highlight');
            console.log('✅ Added highlight to table row');
          } else {
            // For cards or other elements, find the closest row/card container
            const container = el.closest('tr, .task-card, .card, [class*="card"]');
            if (container) {
              container.classList.add('task-row-drag-highlight');
              console.log('✅ Added highlight to container:', container.tagName, container.className);
            } else {
              el.classList.add('task-row-drag-highlight');
              console.log('✅ Added highlight to element directly');
            }
          }
        });
        found = true;
        break;
      }
    }

    // Strategy 2: If not found, search by content matching
    if (!found) {
      const allRows = document.querySelectorAll('tr, .task-card, .card');
      for (const row of allRows) {
        const textContent = row.textContent || '';
        if (textContent.includes(taskId)) {
          row.classList.add('task-row-drag-highlight');
          found = true;
          break;
        }
      }
    }
  }, []);

  // Effect to handle production line hover highlighting
  useEffect(() => {
    if (dragOverProductionLineId) {
      console.log('🎯 Production line hover detected:', dragOverProductionLineId);
      const taskId = getTaskIdFromProductionLine(dragOverProductionLineId);
      console.log('🔍 Found task ID for production line:', taskId);
      if (taskId) {
        setHighlightState({
          highlightedTaskId: taskId,
          highlightedProductionLineId: dragOverProductionLineId,
        });
        enhancedApplyHighlight(taskId);
        console.log('✅ Applied highlight to task:', taskId);
      }
    } else if (dragOverTaskId) {
      // Direct task hover (existing functionality)
      console.log('🎯 Direct task hover detected:', dragOverTaskId);
      setHighlightState({
        highlightedTaskId: dragOverTaskId,
        highlightedProductionLineId: null,
      });
      enhancedApplyHighlight(dragOverTaskId);
      console.log('✅ Applied highlight to task:', dragOverTaskId);
    } else {
      // Clear all highlights
      console.log('🧹 Clearing all highlights');
      setHighlightState({
        highlightedTaskId: null,
        highlightedProductionLineId: null,
      });
      enhancedApplyHighlight(null);
    }
  }, [dragOverProductionLineId, dragOverTaskId, getTaskIdFromProductionLine, enhancedApplyHighlight]);

  /**
   * Get CSS classes for a specific task row
   */
  const getTaskRowClasses = useCallback((taskId: string) => {
    return highlightState.highlightedTaskId === taskId ? 'task-row-drag-highlight' : '';
  }, [highlightState.highlightedTaskId]);

  /**
   * Check if a task row should be highlighted
   */
  const isTaskRowHighlighted = useCallback((taskId: string) => {
    return highlightState.highlightedTaskId === taskId;
  }, [highlightState.highlightedTaskId]);

  return {
    highlightedTaskId: highlightState.highlightedTaskId,
    highlightedProductionLineId: highlightState.highlightedProductionLineId,
    getTaskRowClasses,
    isTaskRowHighlighted,
    applyTaskRowHighlight: enhancedApplyHighlight,
  };
}
