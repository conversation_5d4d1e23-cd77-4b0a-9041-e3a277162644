// src/hooks/useTaskRowHighlight.ts
import { useState, useCallback, useEffect } from 'react';

interface UseTaskRowHighlightProps {
  highlightedTaskId: string | null;
}

/**
 * Simplified hook to manage task row highlighting during vehicle drag operations
 * Directly highlights the specified task row with optimized styling
 */
export function useTaskRowHighlight({
  highlightedTaskId,
}: UseTaskRowHighlightProps) {

  /**
   * Apply optimized highlighting to the specified task row
   * Uses efficient DOM manipulation with minimal style changes
   */
  const applyTaskRowHighlight = useCallback((taskId: string | null) => {
    // Remove existing highlights efficiently
    const existingHighlights = document.querySelectorAll('.task-row-highlight');
    existingHighlights.forEach(el => {
      el.classList.remove('task-row-highlight');
    });

    if (!taskId) {
      console.log('🧹 Cleared task row highlights');
      return;
    }

    console.log('🎯 Highlighting task row:', taskId);

    // Find and highlight the task row with optimized selectors
    const selectors = [
      `tr[data-task-id="${taskId}"]`,
      `[data-task-id="${taskId}"].task-card`,
      `tr[data-row-id="${taskId}"]`,
    ];

    let found = false;
    for (const selector of selectors) {
      try {
        const elements = document.querySelectorAll(selector);
        console.log(`🔍 Selector "${selector}" found ${elements.length} elements`);
        if (elements.length > 0) {
          elements.forEach(element => {
            element.classList.add('task-row-highlight');
            console.log('✅ Added highlight to:', element.tagName, element.className);
          });
          found = true;
          break; // Found and highlighted, no need to continue
        }
      } catch (error) {
        // Skip invalid selectors
        continue;
      }
    }

    if (!found) {
      console.log('❌ No elements found for task ID:', taskId);
    }
  }, []);

  // Effect to apply highlighting when task ID changes
  useEffect(() => {
    applyTaskRowHighlight(highlightedTaskId);
  }, [highlightedTaskId, applyTaskRowHighlight]);

  return {
    applyTaskRowHighlight,
  };
}


